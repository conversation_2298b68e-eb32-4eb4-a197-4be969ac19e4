{"project_info": {"project_number": "943327273952", "project_id": "mudahkan-absensi-app", "storage_bucket": "mudahkan-absensi-app.firebasestorage.app"}, "client": [{"client_info": {"mobilesdk_app_id": "1:943327273952:android:c67ec405d5a2b3d0d78185", "android_client_info": {"package_name": "com.example.attendance_app"}}, "oauth_client": [{"client_id": "943327273952-l175e90o56j1rd1jkl4if00beqdknboo.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyA2KLewJmgDRrybl0WplHpe0rqz1K7HMho"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "943327273952-l175e90o56j1rd1jkl4if00beqdknboo.apps.googleusercontent.com", "client_type": 3}, {"client_id": "943327273952-nde8p0nfltdubp8inicj9d5jvk5eag2n.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.mudahkan.attendanceApp"}}]}}}, {"client_info": {"mobilesdk_app_id": "1:943327273952:android:e9c0fd69b070e523d78185", "android_client_info": {"package_name": "com.tamaasrory.work_documentation_app"}}, "oauth_client": [{"client_id": "943327273952-l175e90o56j1rd1jkl4if00beqdknboo.apps.googleusercontent.com", "client_type": 3}], "api_key": [{"current_key": "AIzaSyA2KLewJmgDRrybl0WplHpe0rqz1K7HMho"}], "services": {"appinvite_service": {"other_platform_oauth_client": [{"client_id": "943327273952-l175e90o56j1rd1jkl4if00beqdknboo.apps.googleusercontent.com", "client_type": 3}, {"client_id": "943327273952-nde8p0nfltdubp8inicj9d5jvk5eag2n.apps.googleusercontent.com", "client_type": 2, "ios_info": {"bundle_id": "com.mudahkan.attendanceApp"}}]}}}], "configuration_version": "1"}