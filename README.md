# Aplikasi Dokumentasi Kerja

Aplikasi mobile untuk dokumentasi pekerjaan dengan fitur pengambilan foto, penambahan metadata lokasi dan waktu, serta penyimpanan lokal dengan autentikasi Google Sign-In.

## Fitur Utama

- **Autentikasi**: Login menggunakan Google Sign-In dengan Firebase Auth
- **Kamera**: Ambil foto langsung dari kamera atau pilih dari galeri
- **Watermark Otomatis**:
  - Foto otomatis diberi watermark dengan informasi:
    - Judul dokumentasi
    - Tanggal dan waktu pengambilan (format Indonesia)
    - Lokasi lengkap (alamat)
    - Koordinat GPS (latitude/longitude)
    - Keterangan tambahan
  - **Watermark cerdas** dengan:
    - Background semi-transparan hitam dengan border
    - Text putih dengan emoji yang kontras
    - **Padding konsisten 40px** untuk semua sisi
    - **Text wrapping otomatis** untuk text panjang
    - **Tidak ada text terpotong** - semua text terlihat jelas
    - **Ukuran adaptif** 15-40% dari tinggi foto
  - Foto siap dibagikan via WhatsApp dengan informasi lengkap
- **Metadata Otomatis**:
  - Lokasi GPS (latitude/longitude)
  - Alamat lengkap dari koordinat
  - Tanggal dan waktu pengambilan foto
- **Input Manual**: Tambahkan judul dan keterangan untuk setiap foto
- **Kompresi Cerdas** 🗜️:
  - Kompresi otomatis berdasarkan ukuran foto
  - Kualitas tetap terjaga dengan algoritma cubic interpolation
  - Pengaturan adaptif:
    - Foto > 8MP: Resize ke 1920x1080, quality 80%
    - Foto > 4MP: Resize ke 1920x1080, quality 85%
    - Foto > 2MP: Resize ke 1600x1200, quality 90%
    - Foto < 2MP: Resize ke 1280x960, quality 95%
  - Info real-time ukuran file sebelum dan sesudah kompresi
  - Pengurangan ukuran hingga 70% tanpa kehilangan kualitas visual
- **Penyimpanan**:
  - Foto dengan watermark disimpan ke galeri perangkat
  - Data metadata disimpan di database lokal (SQLite)
- **Riwayat**:
  - List semua dokumentasi foto
  - Filter berdasarkan tanggal
  - Pencarian berdasarkan judul, keterangan, atau lokasi
  - Sorting berdasarkan tanggal atau judul
- **Detail**: Lihat foto dengan informasi lengkap metadata
- **Share ke WhatsApp** 📱:
  - Tombol share di setiap foto (riwayat dan detail)
  - Share langsung ke WhatsApp dengan foto + keterangan lengkap
  - Share ke aplikasi lain (email, Telegram, dll)
  - Share teks saja tanpa foto
  - Quick share dari notifikasi setelah menyimpan
- **Hapus Dokumentasi** 🗑️:
  - Hapus foto individual dengan konfirmasi
  - Hapus semua dokumentasi sekaligus
  - Menghapus file foto dan data dari database
  - Konfirmasi dialog dengan preview data
  - Progress indicator untuk hapus massal

## Teknologi yang Digunakan

- **Flutter**: Framework utama
- **Firebase Auth**: Autentikasi Google Sign-In
- **SQLite**: Database lokal untuk menyimpan metadata
- **Geolocator**: Mendapatkan lokasi GPS
- **Camera**: Mengambil foto
- **Image Gallery Saver**: Menyimpan foto ke galeri

## Setup dan Instalasi

### 1. Prerequisites

- Flutter SDK (versi 3.7.2 atau lebih baru)
- Android Studio / VS Code
- Firebase project

### 2. Clone Repository

```bash
git clone <repository-url>
cd "Aplikasi Dokumentasi Kerja"
```

### 3. Install Dependencies

```bash
flutter pub get
```

### 4. Setup Firebase

1. Buat project baru di [Firebase Console](https://console.firebase.google.com/)
2. Aktifkan Authentication dan pilih Google Sign-In
3. Download file konfigurasi:
   - `google-services.json` untuk Android (letakkan di `android/app/`)
   - `GoogleService-Info.plist` untuk iOS (letakkan di `ios/Runner/`)
4. Update file `lib/firebase_options.dart` dengan konfigurasi project Anda

### 5. Konfigurasi Android

1. Update `android/app/build.gradle`:
```gradle
android {
    compileSdkVersion 34

    defaultConfig {
        applicationId "com.tamaasrory.work_documentation_app"
        minSdkVersion 21
        targetSdkVersion 34
    }
}
```

2. Tambahkan di `android/app/build.gradle` (dependencies):
```gradle
implementation 'com.google.firebase:firebase-auth'
implementation 'com.google.android.gms:play-services-auth'
```

### 6. Konfigurasi iOS

1. Update `ios/Runner/Info.plist` dengan URL scheme untuk Google Sign-In
2. Pastikan iOS deployment target minimal 11.0

### 7. Jalankan Aplikasi

```bash
flutter run
```

## Struktur Project

```
lib/
├── main.dart                 # Entry point aplikasi
├── firebase_options.dart     # Konfigurasi Firebase
├── models/
│   └── photo_documentation.dart  # Model data dokumentasi
├── services/
│   ├── auth_service.dart     # Service autentikasi
│   ├── camera_service.dart   # Service kamera dan galeri
│   ├── database_service.dart # Service database lokal
│   └── location_service.dart # Service lokasi GPS
└── screens/
    ├── login_screen.dart     # Screen login
    ├── home_screen.dart      # Screen dashboard
    ├── camera_screen.dart    # Screen ambil foto
    ├── history_screen.dart   # Screen riwayat
    └── photo_detail_screen.dart # Screen detail foto
```

## Permissions

Aplikasi memerlukan permissions berikut:

### Android
- `CAMERA`: Mengakses kamera
- `WRITE_EXTERNAL_STORAGE`: Menyimpan foto
- `READ_EXTERNAL_STORAGE`: Membaca foto
- `ACCESS_FINE_LOCATION`: Mendapatkan lokasi GPS
- `ACCESS_COARSE_LOCATION`: Mendapatkan lokasi GPS
- `INTERNET`: Koneksi internet untuk autentikasi

### iOS
- `NSCameraUsageDescription`: Akses kamera
- `NSPhotoLibraryUsageDescription`: Akses galeri foto
- `NSLocationWhenInUseUsageDescription`: Akses lokasi

## Cara Penggunaan

1. **Login**: Buka aplikasi dan login menggunakan akun Google
2. **Ambil Foto**: Tap tombol "Ambil Foto" di dashboard
3. **Input Data**: Isi judul dan keterangan foto
4. **Simpan**: Foto akan otomatis diproses dengan:
   - 🗜️ **Kompresi cerdas** untuk mengurangi ukuran file
   - 📋 **Watermark** dengan informasi lengkap:
     - Judul dokumentasi
     - Tanggal lengkap (format Indonesia)
     - Waktu pengambilan
     - Alamat lokasi
     - Koordinat GPS
     - Keterangan tambahan
   - 📊 **Info kompresi** ditampilkan (ukuran sebelum/sesudah)
5. **Berbagi Mudah**:
   - 📱 **Tombol share** di setiap foto (riwayat dan detail)
   - 💬 **Share ke WhatsApp** langsung dengan foto + keterangan
   - 📤 **Share ke aplikasi lain** (email, Telegram, dll)
   - 📝 **Share teks saja** tanpa foto
   - 🚀 **Quick share** dari notifikasi setelah menyimpan
6. **Lihat Riwayat**: Akses semua dokumentasi melalui menu "Riwayat"
7. **Filter/Search**: Gunakan fitur pencarian dan filter tanggal
8. **Detail**: Tap foto untuk melihat detail lengkap
9. **Hapus Dokumentasi**:
   - 🗑️ **Hapus individual**: Tap tombol hapus merah di setiap foto
   - 🗑️ **Hapus semua**: Menu "Hapus Semua" di AppBar riwayat
   - ⚠️ **Konfirmasi**: Dialog konfirmasi dengan preview data
   - 🔄 **Progress**: Loading indicator untuk hapus massal
   - 💾 **Permanent**: Menghapus file foto dan data database

### Contoh Watermark
Foto yang dihasilkan akan memiliki watermark di bagian bawah dengan format:

**Contoh 1 - Text Normal:**
```
📋 Inspeksi Jembatan Utama
📅 Senin, 25 Desember 2023
🕐 14:30:25
📍 Jl. Sudirman No. 123, Jakarta Pusat, DKI Jakarta
🌐 Lat: -6.208763, Lng: 106.845599
📝 Kondisi struktur baik, perlu pengecatan ulang
```

**Contoh 2 - Text Panjang (Auto Wrap):**
```
📋 Inspeksi Rutin Infrastruktur Jembatan Layang
     Tol Jakarta-Cikampek KM 15
📅 Senin, 25 Desember 2023
🕐 14:30:25
📍 Jl. Tol Jakarta-Cikampek KM 15, Bekasi Timur,
     Kota Bekasi, Jawa Barat, Indonesia
🌐 Lat: -6.208763, Lng: 106.845599
📝 Kondisi struktur secara keseluruhan masih baik,
     namun ditemukan beberapa retakan kecil pada
     bagian sambungan yang perlu diperbaiki segera
```

**Fitur Watermark:**
- ✅ **Padding konsisten 40px** - Jarak yang sama untuk semua sisi foto
- ✅ **Text wrapping** - Text panjang otomatis dibungkus ke baris baru
- ✅ **Tidak terpotong** - Semua text dijamin terlihat dalam foto
- ✅ **Kontras tinggi** - Background hitam semi-transparan + text putih

## Troubleshooting

### Error Firebase
- Pastikan file `google-services.json` dan `GoogleService-Info.plist` sudah benar
- Periksa konfigurasi di `firebase_options.dart`

### Error Permission
- Pastikan semua permissions sudah ditambahkan di manifest
- Test di device fisik untuk permission lokasi dan kamera

### Error Build
- Jalankan `flutter clean` dan `flutter pub get`
- Pastikan Android SDK dan tools sudah update

## Kontribusi

1. Fork repository
2. Buat branch fitur baru
3. Commit perubahan
4. Push ke branch
5. Buat Pull Request

## Lisensi

MIT License
