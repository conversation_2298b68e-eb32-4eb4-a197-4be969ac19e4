<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>Work Documentation App</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>work_documentation_app</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>$(FLUTTER_BUILD_NAME)</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleVersion</key>
	<string>$(FLUTTER_BUILD_NUMBER)</string>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>

	<!-- Camera Permission -->
	<key>NSCameraUsageDescription</key>
	<string>Aplikasi ini memerlukan akses kamera untuk mengambil foto dokumentasi kerja</string>

	<!-- Photo Library Permission -->
	<key>NSPhotoLibraryUsageDescription</key>
	<string>Aplikasi ini memerlukan akses galeri foto untuk menyimpan dan memilih foto dokumentasi</string>

	<!-- Location Permission -->
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>Aplikasi ini memerlukan akses lokasi untuk menambahkan informasi lokasi pada dokumentasi foto</string>

	<!-- Location Always Permission -->
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>Aplikasi ini memerlukan akses lokasi untuk menambahkan informasi lokasi pada dokumentasi foto</string>

	<!-- URL Schemes for Google Sign-In -->
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleURLName</key>
			<string>com.tamaasrory.workDocumentationApp</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.googleusercontent.apps.943327273952-t57ecbgajdom7p1tl9a0d4ecf9vddcr2</string>
			</array>
		</dict>
	</array>
</dict>
</plist>
