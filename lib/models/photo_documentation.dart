class PhotoDocumentation {
  final int? id;
  final String imagePath;
  final String title;
  final String description;
  final double latitude;
  final double longitude;
  final String locationName;
  final DateTime capturedAt;
  final String userId;

  PhotoDocumentation({
    this.id,
    required this.imagePath,
    required this.title,
    required this.description,
    required this.latitude,
    required this.longitude,
    required this.locationName,
    required this.capturedAt,
    required this.userId,
  });

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'imagePath': imagePath,
      'title': title,
      'description': description,
      'latitude': latitude,
      'longitude': longitude,
      'locationName': locationName,
      'capturedAt': capturedAt.millisecondsSinceEpoch,
      'userId': userId,
    };
  }

  factory PhotoDocumentation.fromMap(Map<String, dynamic> map) {
    return PhotoDocumentation(
      id: map['id']?.toInt(),
      imagePath: map['imagePath'] ?? '',
      title: map['title'] ?? '',
      description: map['description'] ?? '',
      latitude: map['latitude']?.toDouble() ?? 0.0,
      longitude: map['longitude']?.toDouble() ?? 0.0,
      locationName: map['locationName'] ?? '',
      capturedAt: DateTime.fromMillisecondsSinceEpoch(map['capturedAt']),
      userId: map['userId'] ?? '',
    );
  }

  PhotoDocumentation copyWith({
    int? id,
    String? imagePath,
    String? title,
    String? description,
    double? latitude,
    double? longitude,
    String? locationName,
    DateTime? capturedAt,
    String? userId,
  }) {
    return PhotoDocumentation(
      id: id ?? this.id,
      imagePath: imagePath ?? this.imagePath,
      title: title ?? this.title,
      description: description ?? this.description,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      locationName: locationName ?? this.locationName,
      capturedAt: capturedAt ?? this.capturedAt,
      userId: userId ?? this.userId,
    );
  }
}
