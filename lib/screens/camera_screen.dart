import 'dart:io';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../services/camera_service.dart';
import '../services/location_service.dart';
import '../services/database_service.dart';
import '../services/auth_service.dart';
import '../services/share_service.dart';
import '../models/photo_documentation.dart';
import '../widgets/keyboard_dismissal.dart';

class CameraScreen extends StatefulWidget {
  const CameraScreen({super.key});

  @override
  State<CameraScreen> createState() => _CameraScreenState();
}

class _CameraScreenState extends State<CameraScreen> {
  final CameraService _cameraService = CameraService();
  final LocationService _locationService = LocationService();
  final DatabaseService _databaseService = DatabaseService();
  final AuthService _authService = AuthService();
  final ShareService _shareService = ShareService();

  final TextEditingController _titleController = TextEditingController();
  final TextEditingController _descriptionController = TextEditingController();

  File? _imageFile;
  LocationInfo? _locationInfo;
  bool _isLoading = false;
  bool _isGettingLocation = false;

  @override
  void initState() {
    super.initState();
    _getCurrentLocation();
  }

  Future<void> _getCurrentLocation() async {
    setState(() {
      _isGettingLocation = true;
    });

    try {
      // Check location status first
      final status = await _locationService.checkLocationStatus();

      if (status != LocationStatus.granted) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Akses lokasi diperlukan untuk mengambil foto'),
              backgroundColor: Colors.red,
            ),
          );
        }
        return;
      }

      final locationInfo = await _locationService.getCurrentLocationInfo();
      setState(() {
        _locationInfo = locationInfo;
      });

      if (locationInfo == null && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Gagal mendapatkan lokasi. Pastikan GPS aktif.'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Gagal mendapatkan lokasi: $e'),
            backgroundColor: Colors.orange,
          ),
        );
      }
    } finally {
      setState(() {
        _isGettingLocation = false;
      });
    }
  }

  Future<void> _takePhoto() async {
    // Check location status before taking photo
    final status = await _locationService.checkLocationStatus();
    if (status != LocationStatus.granted) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Akses lokasi diperlukan untuk mengambil foto dokumentasi'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return;
    }

    // Ensure we have location info
    if (_locationInfo == null) {
      await _getCurrentLocation();
      if (_locationInfo == null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Tidak dapat mendapatkan lokasi. Pastikan GPS aktif.'),
              backgroundColor: Colors.red,
            ),
          );
        }
        return;
      }
    }

    try {
      final imageFile = await _cameraService.takePhoto();
      if (imageFile != null) {
        setState(() {
          _imageFile = imageFile;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Gagal mengambil foto: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _pickFromGallery() async {
    // Check location status before picking image
    final status = await _locationService.checkLocationStatus();
    if (status != LocationStatus.granted) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Akses lokasi diperlukan untuk dokumentasi foto'),
            backgroundColor: Colors.red,
          ),
        );
      }
      return;
    }

    // Ensure we have location info
    if (_locationInfo == null) {
      await _getCurrentLocation();
      if (_locationInfo == null) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Tidak dapat mendapatkan lokasi. Pastikan GPS aktif.'),
              backgroundColor: Colors.red,
            ),
          );
        }
        return;
      }
    }

    try {
      final imageFile = await _cameraService.pickImageFromGallery();
      if (imageFile != null) {
        setState(() {
          _imageFile = imageFile;
        });
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Gagal memilih foto: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _saveDocumentation() async {
    if (_imageFile == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Silakan ambil foto terlebih dahulu'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    if (_titleController.text.trim().isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Silakan isi judul dokumentasi'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      // Show compression info to user
      final originalSize = await _imageFile!.length();
      final formattedSize = _formatFileSize(originalSize);

      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Memproses foto ($formattedSize) dengan kompresi cerdas...'),
            backgroundColor: Colors.blue,
            duration: const Duration(seconds: 2),
          ),
        );
      }

      // Save image with watermark containing metadata
      final savedImagePath = await _cameraService.saveImageWithMetadata(
        imageFile: _imageFile!,
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim(),
        locationName: _locationInfo?.address ?? 'Lokasi tidak diketahui',
        latitude: _locationInfo?.latitude ?? 0.0,
        longitude: _locationInfo?.longitude ?? 0.0,
        capturedAt: DateTime.now(),
      );

      if (savedImagePath == null) {
        throw Exception('Gagal menyimpan foto dengan watermark');
      }

      // Get final file size for comparison
      final finalFile = File(savedImagePath);
      final finalSize = await finalFile.length();
      final reductionPercent = ((originalSize - finalSize) / originalSize * 100);

      // Create photo documentation
      final photoDoc = PhotoDocumentation(
        imagePath: savedImagePath,
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim(),
        latitude: _locationInfo?.latitude ?? 0.0,
        longitude: _locationInfo?.longitude ?? 0.0,
        locationName: _locationInfo?.address ?? 'Lokasi tidak diketahui',
        capturedAt: DateTime.now(),
        userId: _authService.userId,
      );

      // Save to database
      await _databaseService.insertPhotoDocumentation(photoDoc);

      if (mounted) {
        // Show success message with share option
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              'Dokumentasi berhasil disimpan!\n'
              'Ukuran: ${_formatFileSize(originalSize)} → ${_formatFileSize(finalSize)} '
              '(${reductionPercent.toStringAsFixed(0)}% lebih kecil)'
            ),
            backgroundColor: Colors.green,
            duration: const Duration(seconds: 4),
            action: SnackBarAction(
              label: 'Bagikan',
              textColor: Colors.white,
              onPressed: () {
                _showShareDialog(photoDoc, File(savedImagePath));
              },
            ),
          ),
        );
        Navigator.of(context).pop(true);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Gagal menyimpan dokumentasi: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      setState(() {
        _isLoading = false;
      });
    }
  }

  String _formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
  }

  void _showShareDialog(PhotoDocumentation photoDoc, File imageFile) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Bagikan Dokumentasi'),
        content: const Text('Pilih cara untuk membagikan dokumentasi ini:'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Batal'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              final success = await _shareService.shareToWhatsApp(
                imageFile: imageFile,
                photoDoc: photoDoc,
              );
              if (mounted && success) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Berhasil membuka WhatsApp, dan membagikan foto'),
                    backgroundColor: Colors.green,
                  ),
                );
              }
            },
            child: const Text('WhatsApp'),
          ),
          TextButton(
            onPressed: () async {
              Navigator.pop(context);
              final success = await _shareService.sharePhoto(
                imageFile: imageFile,
                photoDoc: photoDoc,
              );
              if (mounted && success) {
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content: Text('Berhasil membagikan foto'),
                    backgroundColor: Colors.green,
                  ),
                );
              }
            },
            child: const Text('Lainnya'),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Dokumentasi Baru'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          if (_imageFile != null)
            TextButton(
              onPressed: _isLoading ? null : _saveDocumentation,
              child: _isLoading
                  ? const SizedBox(
                      width: 20,
                      height: 20,
                      child: CircularProgressIndicator(
                        strokeWidth: 2,
                        valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                      ),
                    )
                  : const Text(
                      'Simpan',
                      style: TextStyle(color: Colors.white),
                    ),
            ),
        ],
      ),
      body: KeyboardDismissal(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Image Section
            Container(
              width: double.infinity,
              height: 250,
              decoration: BoxDecoration(
                color: Colors.grey.shade200,
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.grey.shade300),
              ),
              child: _imageFile != null
                  ? ClipRRect(
                      borderRadius: BorderRadius.circular(12),
                      child: Image.file(
                        _imageFile!,
                        fit: BoxFit.cover,
                      ),
                    )
                  : Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          Icons.camera_alt,
                          size: 64,
                          color: Colors.grey.shade400,
                        ),
                        const SizedBox(height: 16),
                        const Text(
                          'Belum ada foto',
                          style: TextStyle(
                            fontSize: 16,
                            color: Colors.grey,
                          ),
                        ),
                      ],
                    ),
            ),

            const SizedBox(height: 16),

            // Camera Buttons
            Row(
              children: [
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _takePhoto,
                    icon: const Icon(Icons.camera_alt),
                    label: const Text('Ambil Foto'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: _pickFromGallery,
                    icon: const Icon(Icons.photo_library),
                    label: const Text('Pilih Foto'),
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.green,
                      foregroundColor: Colors.white,
                      padding: const EdgeInsets.symmetric(vertical: 12),
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 24),

            // Title Input
            const Text(
              'Judul Dokumentasi *',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _titleController,
              textInputAction: TextInputAction.next,
              decoration: const InputDecoration(
                hintText: 'Masukkan judul dokumentasi',
                border: OutlineInputBorder(),
              ),
              onSubmitted: (value) {
                // Move focus to description field
                FocusScope.of(context).nextFocus();
              },
            ),

            const SizedBox(height: 16),

            // Description Input
            const Text(
              'Keterangan',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            TextField(
              controller: _descriptionController,
              maxLines: 3,
              textInputAction: TextInputAction.done,
              decoration: const InputDecoration(
                hintText: 'Masukkan keterangan tambahan',
                border: OutlineInputBorder(),
              ),
              onSubmitted: (value) {
                // Hide keyboard when done
                FocusScope.of(context).unfocus();
              },
            ),

            const SizedBox(height: 24),

            // Image Info Card (if image is selected)
            if (_imageFile != null) ...[
              Card(
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          const Icon(Icons.image, color: Colors.green),
                          const SizedBox(width: 8),
                          const Text(
                            'Info Foto',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 12),
                      FutureBuilder<int>(
                        future: _imageFile!.length(),
                        builder: (context, snapshot) {
                          if (snapshot.hasData) {
                            final size = snapshot.data!;
                            return Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  'Ukuran asli: ${_formatFileSize(size)}',
                                  style: const TextStyle(fontSize: 14),
                                ),
                                const SizedBox(height: 4),
                                Text(
                                  'Akan dikompres otomatis untuk kualitas optimal',
                                  style: TextStyle(
                                    fontSize: 12,
                                    color: Colors.grey.shade600,
                                  ),
                                ),
                              ],
                            );
                          }
                          return const Text('Menganalisis foto...');
                        },
                      ),
                    ],
                  ),
                ),
              ),
              const SizedBox(height: 16),
            ],

            // Location Info
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        const Icon(Icons.location_on, color: Colors.blue),
                        const SizedBox(width: 8),
                        const Text(
                          'Informasi Lokasi',
                          style: TextStyle(
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        const Spacer(),
                        if (_isGettingLocation)
                          const SizedBox(
                            width: 20,
                            height: 20,
                            child: CircularProgressIndicator(strokeWidth: 2),
                          )
                        else
                          IconButton(
                            onPressed: _getCurrentLocation,
                            icon: const Icon(Icons.refresh),
                          ),
                      ],
                    ),
                    const SizedBox(height: 12),
                    if (_locationInfo != null) ...[
                      Text(
                        'Alamat: ${_locationInfo!.address}',
                        style: const TextStyle(fontSize: 14),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Koordinat: ${_locationInfo!.latitude.toStringAsFixed(6)}, ${_locationInfo!.longitude.toStringAsFixed(6)}',
                        style: const TextStyle(fontSize: 14),
                      ),
                    ] else
                      const Text(
                        'Lokasi tidak tersedia',
                        style: TextStyle(fontSize: 14, color: Colors.grey),
                      ),
                    const SizedBox(height: 8),
                    Text(
                      'Waktu: ${DateFormat('dd/MM/yyyy HH:mm').format(DateTime.now())}',
                      style: const TextStyle(fontSize: 14),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }
}
