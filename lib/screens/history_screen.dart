import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'dart:io';
import '../services/database_service.dart';
import '../services/auth_service.dart';
import '../services/share_service.dart';
import '../models/photo_documentation.dart';
import '../widgets/keyboard_dismissal.dart';
import 'photo_detail_screen.dart';

class HistoryScreen extends StatefulWidget {
  const HistoryScreen({super.key});

  @override
  State<HistoryScreen> createState() => _HistoryScreenState();
}

class _HistoryScreenState extends State<HistoryScreen> {
  final DatabaseService _databaseService = DatabaseService();
  final AuthService _authService = AuthService();
  final ShareService _shareService = ShareService();
  final TextEditingController _searchController = TextEditingController();

  List<PhotoDocumentation> _filteredPhotos = [];
  bool _isLoading = true;
  String _sortBy = 'capturedAt DESC';
  DateTimeRange? _dateRange;

  @override
  void initState() {
    super.initState();
    _loadPhotos();
  }

  Future<void> _loadPhotos() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final photos = await _databaseService.getFilteredPhotoDocumentations(
        userId: _authService.userId,
        searchQuery: _searchController.text.trim().isEmpty ? null : _searchController.text.trim(),
        startDate: _dateRange?.start,
        endDate: _dateRange?.end,
        orderBy: _sortBy,
      );

      setState(() {
        _filteredPhotos = photos;
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading photos: $e');
      setState(() {
        _isLoading = false;
      });
    }
  }

  void _showSortOptions() {
    showModalBottomSheet(
      context: context,
      builder:
          (context) => Container(
            padding: const EdgeInsets.all(16),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                const Text('Urutkan berdasarkan', style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                const SizedBox(height: 16),
                ListTile(
                  title: const Text('Tanggal Terbaru'),
                  leading: Radio<String>(
                    value: 'capturedAt DESC',
                    groupValue: _sortBy,
                    onChanged: (value) {
                      setState(() {
                        _sortBy = value!;
                      });
                      Navigator.pop(context);
                      _loadPhotos();
                    },
                  ),
                ),
                ListTile(
                  title: const Text('Tanggal Terlama'),
                  leading: Radio<String>(
                    value: 'capturedAt ASC',
                    groupValue: _sortBy,
                    onChanged: (value) {
                      setState(() {
                        _sortBy = value!;
                      });
                      Navigator.pop(context);
                      _loadPhotos();
                    },
                  ),
                ),
                ListTile(
                  title: const Text('Judul A-Z'),
                  leading: Radio<String>(
                    value: 'title ASC',
                    groupValue: _sortBy,
                    onChanged: (value) {
                      setState(() {
                        _sortBy = value!;
                      });
                      Navigator.pop(context);
                      _loadPhotos();
                    },
                  ),
                ),
                ListTile(
                  title: const Text('Judul Z-A'),
                  leading: Radio<String>(
                    value: 'title DESC',
                    groupValue: _sortBy,
                    onChanged: (value) {
                      setState(() {
                        _sortBy = value!;
                      });
                      Navigator.pop(context);
                      _loadPhotos();
                    },
                  ),
                ),
              ],
            ),
          ),
    );
  }

  Future<void> _selectDateRange() async {
    final DateTimeRange? picked = await showDateRangePicker(
      context: context,
      firstDate: DateTime(2020),
      lastDate: DateTime.now(),
      initialDateRange: _dateRange,
    );

    if (picked != null) {
      setState(() {
        _dateRange = picked;
      });
      _loadPhotos();
    }
  }

  void _clearDateFilter() {
    setState(() {
      _dateRange = null;
    });
    _loadPhotos();
  }

  Future<void> _quickShareToWhatsApp(PhotoDocumentation photo) async {
    try {
      final File imageFile = File(photo.imagePath);

      if (!await imageFile.exists()) {
        if (mounted) {
          ScaffoldMessenger.of(
            context,
          ).showSnackBar(const SnackBar(content: Text('File foto tidak ditemukan'), backgroundColor: Colors.red));
        }
        return;
      }

      final success = await _shareService.shareToWhatsApp(imageFile: imageFile, photoDoc: photo);

      if (mounted && success) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Berhasil membuka WhatsApp'),
            backgroundColor: Colors.green,
            duration: Duration(seconds: 2),
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(
          context,
        ).showSnackBar(SnackBar(content: Text('Gagal membagikan: $e'), backgroundColor: Colors.red));
      }
    }
  }

  Future<void> _deletePhoto(PhotoDocumentation photo) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Hapus Dokumentasi'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text('Apakah Anda yakin ingin menghapus dokumentasi ini?'),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.grey.shade100,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    photo.title,
                    style: const TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 14,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    DateFormat('dd/MM/yyyy HH:mm').format(photo.capturedAt),
                    style: TextStyle(
                      fontSize: 12,
                      color: Colors.grey.shade600,
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 12),
            const Text(
              'Foto dan data akan dihapus permanen.',
              style: TextStyle(
                color: Colors.red,
                fontSize: 12,
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Batal'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Hapus'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        // Delete from database
        await _databaseService.deletePhotoDocumentation(photo.id!);

        // Delete image file
        final File imageFile = File(photo.imagePath);
        if (await imageFile.exists()) {
          await imageFile.delete();
        }

        // Reload photos
        await _loadPhotos();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Dokumentasi berhasil dihapus'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Gagal menghapus dokumentasi: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  Future<void> _deleteAllPhotos() async {
    if (_filteredPhotos.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Tidak ada dokumentasi untuk dihapus'),
          backgroundColor: Colors.orange,
        ),
      );
      return;
    }

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Hapus Semua Dokumentasi'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Apakah Anda yakin ingin menghapus semua ${_filteredPhotos.length} dokumentasi?'),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Colors.red.shade50,
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: Colors.red.shade200),
              ),
              child: const Row(
                children: [
                  Icon(Icons.warning, color: Colors.red, size: 20),
                  SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      'Semua foto dan data akan dihapus permanen dan tidak dapat dikembalikan.',
                      style: TextStyle(
                        color: Colors.red,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Batal'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Hapus Semua'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        // Show loading
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Row(
                children: [
                  SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  ),
                  SizedBox(width: 16),
                  Text('Menghapus semua dokumentasi...'),
                ],
              ),
              duration: Duration(seconds: 10),
            ),
          );
        }

        int deletedCount = 0;

        // Delete all photos
        for (PhotoDocumentation photo in _filteredPhotos) {
          try {
            // Delete from database
            await _databaseService.deletePhotoDocumentation(photo.id!);

            // Delete image file
            final File imageFile = File(photo.imagePath);
            if (await imageFile.exists()) {
              await imageFile.delete();
            }

            deletedCount++;
          } catch (e) {
            print('Error deleting photo ${photo.id}: $e');
          }
        }

        // Reload photos
        await _loadPhotos();

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('$deletedCount dokumentasi berhasil dihapus'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Gagal menghapus dokumentasi: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Riwayat Dokumentasi'),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _showSortOptions,
            icon: const Icon(Icons.sort),
            tooltip: 'Urutkan',
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              if (value == 'delete_all') {
                _deleteAllPhotos();
              }
            },
            itemBuilder: (context) => [
              PopupMenuItem(
                value: 'delete_all',
                enabled: _filteredPhotos.isNotEmpty,
                child: Row(
                  children: [
                    Icon(
                      Icons.delete_sweep,
                      color: _filteredPhotos.isNotEmpty ? Colors.red : Colors.grey,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      'Hapus Semua (${_filteredPhotos.length})',
                      style: TextStyle(
                        color: _filteredPhotos.isNotEmpty ? Colors.red : Colors.grey,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: KeyboardDismissal(
        child: Column(
          children: [
            // Search and Filter Section
            Container(
            padding: const EdgeInsets.all(16),
            color: Colors.grey.shade50,
            child: Column(
              children: [
                // Search Bar
                TextField(
                  controller: _searchController,
                  textInputAction: TextInputAction.search,
                  decoration: InputDecoration(
                    hintText: 'Cari dokumentasi...',
                    prefixIcon: const Icon(Icons.search),
                    suffixIcon:
                        _searchController.text.isNotEmpty
                            ? IconButton(
                              onPressed: () {
                                _searchController.clear();
                                _loadPhotos();
                                FocusScope.of(context).unfocus();
                              },
                              icon: const Icon(Icons.clear),
                            )
                            : null,
                    border: OutlineInputBorder(borderRadius: BorderRadius.circular(12)),
                    filled: true,
                    fillColor: Colors.white,
                  ),
                  onChanged: (value) {
                    // Debounce search
                    Future.delayed(const Duration(milliseconds: 500), () {
                      if (_searchController.text == value) {
                        _loadPhotos();
                      }
                    });
                  },
                  onSubmitted: (value) {
                    // Hide keyboard when search is submitted
                    FocusScope.of(context).unfocus();
                    _loadPhotos();
                  },
                ),

                const SizedBox(height: 12),

                // Date Filter
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: _selectDateRange,
                        icon: const Icon(Icons.date_range),
                        label: Text(
                          _dateRange != null
                              ? '${DateFormat('dd/MM/yy').format(_dateRange!.start)} - ${DateFormat('dd/MM/yy').format(_dateRange!.end)}'
                              : 'Filter Tanggal',
                        ),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: _dateRange != null ? Colors.blue : Colors.grey.shade300,
                          foregroundColor: _dateRange != null ? Colors.white : Colors.black,
                        ),
                      ),
                    ),
                    if (_dateRange != null) ...[
                      const SizedBox(width: 8),
                      IconButton(
                        onPressed: _clearDateFilter,
                        icon: const Icon(Icons.clear),
                        style: IconButton.styleFrom(backgroundColor: Colors.red, foregroundColor: Colors.white),
                      ),
                    ],
                  ],
                ),
              ],
            ),
          ),

          // Photos List
          Expanded(
            child:
                _isLoading
                    ? const Center(child: CircularProgressIndicator())
                    : _filteredPhotos.isEmpty
                    ? const Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(Icons.photo_library_outlined, size: 64, color: Colors.grey),
                          SizedBox(height: 16),
                          Text('Tidak ada dokumentasi ditemukan', style: TextStyle(fontSize: 16, color: Colors.grey)),
                        ],
                      ),
                    )
                    : RefreshIndicator(
                      onRefresh: _loadPhotos,
                      child: ListView.builder(
                        padding: const EdgeInsets.all(16),
                        itemCount: _filteredPhotos.length,
                        itemBuilder: (context, index) {
                          final photo = _filteredPhotos[index];
                          return Card(
                            margin: const EdgeInsets.only(bottom: 12),
                            child: InkWell(
                              onTap: () {
                                Navigator.push(
                                  context,
                                  MaterialPageRoute(builder: (context) => PhotoDetailScreen(photo: photo)),
                                );
                              },
                              borderRadius: BorderRadius.circular(12),
                              child: Padding(
                                padding: const EdgeInsets.all(12),
                                child: Row(
                                  children: [
                                    // Thumbnail
                                    ClipRRect(
                                      borderRadius: BorderRadius.circular(8),
                                      child: Container(
                                        width: 80,
                                        height: 80,
                                        color: Colors.grey.shade300,
                                        child:
                                            File(photo.imagePath).existsSync()
                                                ? Image.file(File(photo.imagePath), fit: BoxFit.cover)
                                                : const Icon(Icons.image, size: 40, color: Colors.grey),
                                      ),
                                    ),

                                    const SizedBox(width: 12),

                                    // Content
                                    Expanded(
                                      child: Column(
                                        crossAxisAlignment: CrossAxisAlignment.start,
                                        children: [
                                          Text(
                                            photo.title,
                                            style: const TextStyle(fontSize: 16, fontWeight: FontWeight.bold),
                                            maxLines: 2,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                          const SizedBox(height: 4),
                                          Text(
                                            photo.description.isNotEmpty ? photo.description : 'Tidak ada keterangan',
                                            style: TextStyle(fontSize: 14, color: Colors.grey.shade600),
                                            maxLines: 2,
                                            overflow: TextOverflow.ellipsis,
                                          ),
                                          const SizedBox(height: 8),
                                          Row(
                                            children: [
                                              Icon(Icons.location_on, size: 16, color: Colors.grey.shade600),
                                              const SizedBox(width: 4),
                                              Expanded(
                                                child: Text(
                                                  photo.locationName,
                                                  style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                                                  maxLines: 1,
                                                  overflow: TextOverflow.ellipsis,
                                                ),
                                              ),
                                            ],
                                          ),
                                          const SizedBox(height: 4),
                                          Text(
                                            DateFormat('dd/MM/yyyy HH:mm').format(photo.capturedAt),
                                            style: TextStyle(fontSize: 12, color: Colors.grey.shade600),
                                          ),
                                        ],
                                      ),
                                    ),

                                    // Action Buttons
                                    Column(
                                      children: [
                                        // Share Button
                                        IconButton(
                                          onPressed: () => _quickShareToWhatsApp(photo),
                                          icon: const Icon(Icons.share, color: Colors.green),
                                          tooltip: 'Bagikan ke WhatsApp',
                                          iconSize: 20,
                                        ),
                                        Text('Share', style: TextStyle(fontSize: 10, color: Colors.grey.shade600)),
                                        const SizedBox(height: 8),
                                        // Delete Button
                                        IconButton(
                                          onPressed: () => _deletePhoto(photo),
                                          icon: const Icon(Icons.delete, color: Colors.red),
                                          tooltip: 'Hapus',
                                          iconSize: 20,
                                        ),
                                        Text('Hapus', style: TextStyle(fontSize: 10, color: Colors.grey.shade600)),
                                      ],
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          );
                        },
                      ),
                    ),
          ),
        ],
        ),
      ),
    );
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}
