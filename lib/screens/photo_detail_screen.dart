import 'dart:io';
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import '../models/photo_documentation.dart';
import '../services/database_service.dart';
import '../services/camera_service.dart';
import '../services/share_service.dart';

class PhotoDetailScreen extends StatefulWidget {
  final PhotoDocumentation photo;

  const PhotoDetailScreen({
    super.key,
    required this.photo,
  });

  @override
  State<PhotoDetailScreen> createState() => _PhotoDetailScreenState();
}

class _PhotoDetailScreenState extends State<PhotoDetailScreen> {
  final DatabaseService _databaseService = DatabaseService();
  final CameraService _cameraService = CameraService();
  final ShareService _shareService = ShareService();

  Future<void> _deletePhoto() async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Hapus Dokumentasi'),
        content: const Text('<PERSON><PERSON><PERSON>h Anda yakin ingin menghapus dokumentasi ini?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Batal'),
          ),
          TextButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: TextButton.styleFrom(foregroundColor: Colors.red),
            child: const Text('Hapus'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      try {
        // Delete from database
        await _databaseService.deletePhotoDocumentation(widget.photo.id!);

        // Delete image file
        await _cameraService.deleteImageFile(widget.photo.imagePath);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Dokumentasi berhasil dihapus'),
              backgroundColor: Colors.green,
            ),
          );
          Navigator.of(context).pop(true);
        }
      } catch (e) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Gagal menghapus dokumentasi: $e'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }

  Future<void> _sharePhoto() async {
    try {
      final File imageFile = File(widget.photo.imagePath);

      if (!await imageFile.exists()) {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('File foto tidak ditemukan'),
              backgroundColor: Colors.red,
            ),
          );
        }
        return;
      }

      // Show share options
      _showShareOptions(imageFile);
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Gagal membagikan foto: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  void _showShareOptions(File imageFile) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Bagikan Dokumentasi',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),

            // Share to WhatsApp
            ListTile(
              leading: const Icon(Icons.message, color: Colors.green),
              title: const Text('WhatsApp'),
              subtitle: const Text('Bagikan foto dengan keterangan ke WhatsApp'),
              onTap: () async {
                Navigator.pop(context);
                final success = await _shareService.shareToWhatsApp(
                  imageFile: imageFile,
                  photoDoc: widget.photo,
                );
                if (mounted && success) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Berhasil membuka WhatsApp'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              },
            ),

            // General Share
            ListTile(
              leading: const Icon(Icons.share, color: Colors.blue),
              title: const Text('Aplikasi Lain'),
              subtitle: const Text('Bagikan ke aplikasi lain'),
              onTap: () async {
                Navigator.pop(context);
                final success = await _shareService.sharePhoto(
                  imageFile: imageFile,
                  photoDoc: widget.photo,
                );
                if (mounted && success) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Berhasil membagikan foto'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              },
            ),

            // Share Text Only
            ListTile(
              leading: const Icon(Icons.text_fields, color: Colors.orange),
              title: const Text('Teks Saja'),
              subtitle: const Text('Bagikan informasi dokumentasi tanpa foto'),
              onTap: () async {
                Navigator.pop(context);
                final success = await _shareService.shareTextOnly(
                  photoDoc: widget.photo,
                );
                if (mounted && success) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Berhasil membagikan informasi'),
                      backgroundColor: Colors.green,
                    ),
                  );
                }
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showImageFullscreen() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => Scaffold(
          backgroundColor: Colors.black,
          appBar: AppBar(
            backgroundColor: Colors.black,
            foregroundColor: Colors.white,
            title: Text(widget.photo.title),
            actions: [
              IconButton(
                onPressed: _sharePhoto,
                icon: const Icon(Icons.share),
                tooltip: 'Bagikan',
              ),
            ],
          ),
          body: Center(
            child: InteractiveViewer(
              child: File(widget.photo.imagePath).existsSync()
                  ? Image.file(
                      File(widget.photo.imagePath),
                      fit: BoxFit.contain,
                    )
                  : Container(
                      width: double.infinity,
                      height: 300,
                      color: Colors.grey.shade300,
                      child: const Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.broken_image,
                            size: 64,
                            color: Colors.grey,
                          ),
                          SizedBox(height: 16),
                          Text(
                            'Gambar tidak ditemukan',
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.grey,
                            ),
                          ),
                        ],
                      ),
                    ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(widget.photo.title),
        backgroundColor: Colors.blue,
        foregroundColor: Colors.white,
        actions: [
          IconButton(
            onPressed: _sharePhoto,
            icon: const Icon(Icons.share),
            tooltip: 'Bagikan',
          ),
          PopupMenuButton<String>(
            onSelected: (value) {
              if (value == 'delete') {
                _deletePhoto();
              }
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: 'delete',
                child: Row(
                  children: [
                    Icon(Icons.delete, color: Colors.red),
                    SizedBox(width: 8),
                    Text('Hapus'),
                  ],
                ),
              ),
            ],
          ),
        ],
      ),
      body: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Image Section
            GestureDetector(
              onTap: _showImageFullscreen,
              child: Container(
                width: double.infinity,
                height: 300,
                color: Colors.grey.shade200,
                child: File(widget.photo.imagePath).existsSync()
                    ? Image.file(
                        File(widget.photo.imagePath),
                        fit: BoxFit.cover,
                      )
                    : const Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.broken_image,
                            size: 64,
                            color: Colors.grey,
                          ),
                          SizedBox(height: 16),
                          Text(
                            'Gambar tidak ditemukan',
                            style: TextStyle(
                              fontSize: 16,
                              color: Colors.grey,
                            ),
                          ),
                        ],
                      ),
              ),
            ),

            // Content Section
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title
                  Text(
                    widget.photo.title,
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Description
                  if (widget.photo.description.isNotEmpty) ...[
                    const Text(
                      'Keterangan',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Text(
                      widget.photo.description,
                      style: const TextStyle(fontSize: 16),
                    ),
                    const SizedBox(height: 24),
                  ],

                  // Location Info
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Row(
                            children: [
                              Icon(Icons.location_on, color: Colors.blue),
                              SizedBox(width: 8),
                              Text(
                                'Informasi Lokasi',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 12),
                          Text(
                            'Alamat: ${widget.photo.locationName}',
                            style: const TextStyle(fontSize: 16),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Koordinat: ${widget.photo.latitude.toStringAsFixed(6)}, ${widget.photo.longitude.toStringAsFixed(6)}',
                            style: const TextStyle(fontSize: 16),
                          ),
                        ],
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Date Time Info
                  Card(
                    child: Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          const Row(
                            children: [
                              Icon(Icons.access_time, color: Colors.blue),
                              SizedBox(width: 8),
                              Text(
                                'Waktu Pengambilan',
                                style: TextStyle(
                                  fontSize: 18,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 12),
                          Text(
                            'Tanggal: ${DateFormat('EEEE, dd MMMM yyyy', 'id_ID').format(widget.photo.capturedAt)}',
                            style: const TextStyle(fontSize: 16),
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'Waktu: ${DateFormat('HH:mm:ss').format(widget.photo.capturedAt)}',
                            style: const TextStyle(fontSize: 16),
                          ),
                        ],
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}
