// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        return macos;
      case TargetPlatform.windows:
        return windows;
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyCq2OHUjE2S6yqHsiOVluzuVV6_iMsrqFQ',
    appId: '1:943327273952:web:a6e385921a3359f8d78185',
    messagingSenderId: '943327273952',
    projectId: 'mudahkan-absensi-app',
    authDomain: 'mudahkan-absensi-app.firebaseapp.com',
    storageBucket: 'mudahkan-absensi-app.firebasestorage.app',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyA2KLewJmgDRrybl0WplHpe0rqz1K7HMho',
    appId: '1:943327273952:android:e9c0fd69b070e523d78185',
    messagingSenderId: '943327273952',
    projectId: 'mudahkan-absensi-app',
    storageBucket: 'mudahkan-absensi-app.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyAWe9E2M-ClDw-SZiNKRcZp_0o6Ufx_3uo',
    appId: '1:943327273952:ios:6b9ef6baba3f308bd78185',
    messagingSenderId: '943327273952',
    projectId: 'mudahkan-absensi-app',
    storageBucket: 'mudahkan-absensi-app.firebasestorage.app',
    iosClientId: '943327273952-t57ecbgajdom7p1tl9a0d4ecf9vddcr2.apps.googleusercontent.com',
    iosBundleId: 'com.tamaasrory.workDocumentationApp',
  );

  static const FirebaseOptions macos = FirebaseOptions(
    apiKey: 'AIzaSyAWe9E2M-ClDw-SZiNKRcZp_0o6Ufx_3uo',
    appId: '1:943327273952:ios:97583856a9022d7ad78185',
    messagingSenderId: '943327273952',
    projectId: 'mudahkan-absensi-app',
    storageBucket: 'mudahkan-absensi-app.firebasestorage.app',
    iosClientId: '943327273952-91hj9ui9comp7v106rtu6fssncd9ncfn.apps.googleusercontent.com',
    iosBundleId: 'com.example.workDocumentationApp',
  );

  static const FirebaseOptions windows = FirebaseOptions(
    apiKey: 'your-windows-api-key',
    appId: 'your-windows-app-id',
    messagingSenderId: 'your-sender-id',
    projectId: 'your-project-id',
    authDomain: 'your-project-id.firebaseapp.com',
    storageBucket: 'your-project-id.appspot.com',
  );
}