import 'package:flutter/material.dart';

/// Widget wrapper yang otomatis menyembunyikan keyboard ketika user tap di area kosong
class KeyboardDismissal extends StatelessWidget {
  final Widget child;
  final bool dismissOnTap;

  const KeyboardDismissal({
    super.key,
    required this.child,
    this.dismissOnTap = true,
  });

  @override
  Widget build(BuildContext context) {
    if (!dismissOnTap) {
      return child;
    }

    return GestureDetector(
      onTap: () {
        // Unfocus semua text field dan sembunyikan keyboard
        FocusScope.of(context).unfocus();
      },
      behavior: HitTestBehavior.opaque,
      child: child,
    );
  }
}

/// Extension untuk mempermudah penggunaan
extension KeyboardDismissalExtension on Widget {
  /// Wrap widget dengan KeyboardDismissal
  Widget dismissKeyboard({bool dismissOnTap = true}) {
    return KeyboardDismissal(
      dismissOnTap: dismissOnTap,
      child: this,
    );
  }
}

/// Mixin untuk State yang membutuhkan keyboard management
mixin KeyboardDismissalMixin<T extends StatefulWidget> on State<T>, WidgetsBindingObserver {
  /// Sembunyikan keyboard
  void hideKeyboard() {
    FocusScope.of(context).unfocus();
  }

  /// Check apakah keyboard sedang tampil
  bool get isKeyboardVisible {
    return MediaQuery.of(context).viewInsets.bottom > 0;
  }

  /// Callback ketika keyboard visibility berubah
  void onKeyboardVisibilityChanged(bool isVisible) {
    // Override di subclass jika diperlukan
  }

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeMetrics() {
    super.didChangeMetrics();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (mounted) {
        onKeyboardVisibilityChanged(isKeyboardVisible);
      }
    });
  }
}
