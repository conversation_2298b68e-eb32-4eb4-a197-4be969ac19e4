import 'package:flutter/material.dart';
import '../services/location_service.dart';
import '../screens/location_permission_screen.dart';

class LocationGuard extends StatefulWidget {
  final Widget child;
  final bool requireLocation;

  const LocationGuard({
    super.key,
    required this.child,
    this.requireLocation = true,
  });

  @override
  State<LocationGuard> createState() => _LocationGuardState();
}

class _LocationGuardState extends State<LocationGuard> with WidgetsBindingObserver {
  final LocationService _locationService = LocationService();
  bool _isLocationGranted = false;
  bool _isChecking = true;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    if (widget.requireLocation) {
      _checkLocationStatus();
    } else {
      setState(() {
        _isLocationGranted = true;
        _isChecking = false;
      });
    }
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);
    
    // Check location status when app resumes
    if (state == AppLifecycleState.resumed && widget.requireLocation) {
      _checkLocationStatus();
    }
  }

  Future<void> _checkLocationStatus() async {
    if (!widget.requireLocation) return;

    setState(() {
      _isChecking = true;
    });

    try {
      final status = await _locationService.checkLocationStatus();
      setState(() {
        _isLocationGranted = status == LocationStatus.granted;
        _isChecking = false;
      });

      // If location is not granted, show permission screen
      if (!_isLocationGranted && mounted) {
        _showLocationPermissionScreen();
      }
    } catch (e) {
      setState(() {
        _isLocationGranted = false;
        _isChecking = false;
      });
      
      if (mounted) {
        _showLocationPermissionScreen();
      }
    }
  }

  Future<void> _showLocationPermissionScreen() async {
    final result = await Navigator.of(context).push<bool>(
      MaterialPageRoute(
        builder: (context) => const LocationPermissionScreen(),
        settings: const RouteSettings(name: '/location-permission'),
      ),
    );

    if (result == true) {
      setState(() {
        _isLocationGranted = true;
      });
    } else {
      // If user didn't grant permission, check again
      _checkLocationStatus();
    }
  }

  @override
  Widget build(BuildContext context) {
    if (!widget.requireLocation) {
      return widget.child;
    }

    if (_isChecking) {
      return Scaffold(
        body: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              const CircularProgressIndicator(),
              const SizedBox(height: 16),
              Text(
                'Memeriksa akses lokasi...',
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.grey.shade600,
                ),
              ),
            ],
          ),
        ),
      );
    }

    if (!_isLocationGranted) {
      return Scaffold(
        body: Center(
          child: Padding(
            padding: const EdgeInsets.all(24.0),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.location_off,
                  size: 80,
                  color: Colors.grey.shade400,
                ),
                const SizedBox(height: 24),
                const Text(
                  'Akses Lokasi Diperlukan',
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 16),
                Text(
                  'Aplikasi memerlukan akses lokasi untuk berfungsi dengan baik.',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.grey.shade600,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 32),
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton.icon(
                    onPressed: _showLocationPermissionScreen,
                    icon: const Icon(Icons.location_on),
                    label: const Text('Berikan Akses Lokasi'),
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      backgroundColor: Colors.blue,
                      foregroundColor: Colors.white,
                    ),
                  ),
                ),
                const SizedBox(height: 12),
                OutlinedButton.icon(
                  onPressed: _checkLocationStatus,
                  icon: const Icon(Icons.refresh),
                  label: const Text('Periksa Ulang'),
                  style: OutlinedButton.styleFrom(
                    padding: const EdgeInsets.symmetric(vertical: 16),
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    }

    return widget.child;
  }
}
