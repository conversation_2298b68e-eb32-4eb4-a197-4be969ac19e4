import 'dart:io';
import 'dart:typed_data';
import 'package:image/image.dart' as img;
import 'package:intl/intl.dart';

class WatermarkService {
  static final WatermarkService _instance = WatermarkService._internal();
  factory WatermarkService() => _instance;
  WatermarkService._internal();

  /// Add watermark with location, date, and custom text to image
  Future<File?> addWatermarkToImage({
    required File imageFile,
    required String title,
    required String description,
    required String locationName,
    required double latitude,
    required double longitude,
    required DateTime capturedAt,
    int maxWidth = 1920,
    int maxHeight = 1080,
    int quality = 85,
  }) async {
    try {
      // Read the image file
      final Uint8List imageBytes = await imageFile.readAsBytes();
      img.Image? image = img.decodeImage(imageBytes);

      if (image == null) {
        throw Exception('Failed to decode image');
      }

      // Compress image if too large
      image = _compressImage(image, maxWidth, maxHeight);

      // Create watermark text
      final String watermarkText = _createWatermarkText(
        title: title,
        description: description,
        locationName: locationName,
        latitude: latitude,
        longitude: longitude,
        capturedAt: capturedAt,
      );

      // Add watermark to image
      image = _addTextWatermark(image, watermarkText);

      // Save the modified image with compression
      final String outputPath = imageFile.path.replaceAll('.jpg', '_watermarked.jpg');
      final File outputFile = File(outputPath);
      await outputFile.writeAsBytes(img.encodeJpg(image, quality: quality));

      return outputFile;
    } catch (e) {
      print('Error adding watermark: $e');
      return null;
    }
  }

  /// Create watermark text content
  String _createWatermarkText({
    required String title,
    required String description,
    required String locationName,
    required double latitude,
    required double longitude,
    required DateTime capturedAt,
  }) {
    final String formattedDate = DateFormat('dd/MM/yyyy HH:mm:ss').format(capturedAt);
    final String coordinates = '${latitude.toStringAsFixed(6)}, ${longitude.toStringAsFixed(6)}';

    String watermarkText = '$title\n';
    watermarkText += '$formattedDate\n';
    watermarkText += 'Lokasi: $locationName\n';
    watermarkText += 'Koordinat: $coordinates';

    if (description.isNotEmpty) {
      watermarkText += '\nKeterangan: $description';
    }

    return watermarkText;
  }

  /// Add text watermark to image with proper padding and text wrapping
  img.Image _addTextWatermark(img.Image image, String text) {
    // Calculate watermark position and size
    final int imageWidth = image.width;
    final int imageHeight = image.height;

    // Fixed padding 40px for all sides
    final int horizontalPadding = 40;
    final int verticalPadding = 40;

    // Available width for text (image width minus padding on both sides)
    final int availableTextWidth = imageWidth - (horizontalPadding * 2);

    // Split text into lines and wrap long lines
    final List<String> originalLines = text.split('\n');
    final List<String> wrappedLines = [];

    for (String line in originalLines) {
      wrappedLines.addAll(_wrapText(line, availableTextWidth, imageWidth));
    }

    // Calculate font size and line height
    final int fontSize = _calculateFontSize(imageWidth);
    final int lineHeight = (fontSize * 1.3).round();

    // Calculate required watermark height
    final int requiredHeight = (wrappedLines.length * lineHeight) + (verticalPadding * 2);
    final int watermarkHeight = requiredHeight.clamp(
      (imageHeight * 0.15).round(), // Minimum 15% of image height
      (imageHeight * 0.4).round(),  // Maximum 40% of image height
    );
    final int watermarkY = imageHeight - watermarkHeight;

    // Add semi-transparent black background with rounded corners effect
    img.fillRect(
      image,
      x1: 0,
      y1: watermarkY,
      x2: imageWidth,
      y2: imageHeight,
      color: img.ColorRgba8(0, 0, 0, 180), // Semi-transparent black
    );

    // Add subtle border at the top
    img.drawLine(
      image,
      x1: 0,
      y1: watermarkY,
      x2: imageWidth,
      y2: watermarkY,
      color: img.ColorRgba8(255, 255, 255, 100),
      thickness: 2,
    );

    // Draw each line of text with proper spacing
    int currentY = watermarkY + verticalPadding;

    for (int i = 0; i < wrappedLines.length; i++) {
      final String line = wrappedLines[i];

      // Make sure text fits within watermark area
      if (currentY + lineHeight <= imageHeight - verticalPadding) {
        img.drawString(
          image,
          line,
          font: img.arial24,
          x: horizontalPadding,
          y: currentY,
          color: img.ColorRgba8(255, 255, 255, 255), // White text
        );

        currentY += lineHeight;
      } else {
        // If we run out of space, stop drawing
        break;
      }
    }

    return image;
  }

  /// Calculate appropriate font size based on image width
  int _calculateFontSize(int imageWidth) {
    if (imageWidth > 2000) {
      return 48;
    } else if (imageWidth > 1500) {
      return 36;
    } else if (imageWidth > 1000) {
      return 24;
    } else {
      return 18;
    }
  }

  /// Wrap text to fit within available width
  List<String> _wrapText(String text, int availableWidth, int imageWidth) {
    if (text.isEmpty) return [''];

    // Estimate character width based on image size
    final double charWidth = _estimateCharacterWidth(imageWidth);
    final int maxCharsPerLine = (availableWidth / charWidth).floor();

    // If text is short enough, return as is
    if (text.length <= maxCharsPerLine) {
      return [text];
    }

    List<String> wrappedLines = [];
    List<String> words = text.split(' ');
    String currentLine = '';

    for (String word in words) {
      String testLine = currentLine.isEmpty ? word : '$currentLine $word';

      if (testLine.length <= maxCharsPerLine) {
        currentLine = testLine;
      } else {
        // If current line is not empty, add it to wrapped lines
        if (currentLine.isNotEmpty) {
          wrappedLines.add(currentLine);
          currentLine = word;
        } else {
          // If single word is too long, break it
          if (word.length > maxCharsPerLine) {
            wrappedLines.addAll(_breakLongWord(word, maxCharsPerLine));
            currentLine = '';
          } else {
            currentLine = word;
          }
        }
      }
    }

    // Add remaining text
    if (currentLine.isNotEmpty) {
      wrappedLines.add(currentLine);
    }

    return wrappedLines.isEmpty ? [''] : wrappedLines;
  }

  /// Estimate character width based on image size
  double _estimateCharacterWidth(int imageWidth) {
    // Rough estimation based on font size and image width
    final int fontSize = _calculateFontSize(imageWidth);
    return fontSize * 0.6; // Approximate character width ratio
  }

  /// Break long word that doesn't fit in one line
  List<String> _breakLongWord(String word, int maxCharsPerLine) {
    List<String> parts = [];
    for (int i = 0; i < word.length; i += maxCharsPerLine) {
      int endIndex = (i + maxCharsPerLine < word.length)
          ? i + maxCharsPerLine
          : word.length;
      parts.add(word.substring(i, endIndex));
    }
    return parts;
  }

  /// Calculate optimal padding based on image dimensions
  Map<String, int> _calculateOptimalPadding(int imageWidth, int imageHeight) {
    // Base padding percentages
    final double horizontalPaddingPercent = 0.03; // 3% of width
    final double verticalPaddingPercent = 0.02;   // 2% of height

    // Calculate padding with min/max constraints
    final int horizontalPadding = (imageWidth * horizontalPaddingPercent)
        .round()
        .clamp(15, 50); // Min 15px, Max 50px

    final int verticalPadding = (imageHeight * verticalPaddingPercent)
        .round()
        .clamp(10, 40); // Min 10px, Max 40px

    return {
      'horizontal': horizontalPadding,
      'vertical': verticalPadding,
    };
  }

  /// Calculate optimal watermark height based on content and image size
  int _calculateWatermarkHeight({
    required int imageHeight,
    required int lineCount,
    required int lineHeight,
    required int verticalPadding,
  }) {
    // Calculate required height for all content
    final int contentHeight = lineCount * lineHeight;
    final int requiredHeight = contentHeight + (verticalPadding * 2);

    // Set constraints based on image height
    final int minHeight = (imageHeight * 0.15).round(); // Minimum 15%
    final int maxHeight = (imageHeight * 0.4).round();  // Maximum 40%

    return requiredHeight.clamp(minHeight, maxHeight);
  }

  /// Validate text fits within watermark area
  bool _validateTextFits({
    required int availableHeight,
    required int lineCount,
    required int lineHeight,
    required int verticalPadding,
  }) {
    final int requiredHeight = (lineCount * lineHeight) + (verticalPadding * 2);
    return requiredHeight <= availableHeight;
  }

  /// Get text color with good contrast
  img.Color _getOptimalTextColor(img.Color backgroundColor) {
    // Simple contrast calculation - if background is dark, use white text
    final int bgBrightness = (backgroundColor.r + backgroundColor.g + backgroundColor.b) ~/ 3;

    if (bgBrightness < 128) {
      return img.ColorRgba8(255, 255, 255, 255); // White text for dark background
    } else {
      return img.ColorRgba8(0, 0, 0, 255); // Black text for light background
    }
  }

  /// Compress image to reduce file size while maintaining quality
  img.Image _compressImage(img.Image image, int maxWidth, int maxHeight) {
    final int originalWidth = image.width;
    final int originalHeight = image.height;

    // Calculate if resizing is needed
    if (originalWidth <= maxWidth && originalHeight <= maxHeight) {
      return image; // No compression needed
    }

    // Calculate new dimensions maintaining aspect ratio
    double aspectRatio = originalWidth / originalHeight;
    int newWidth = maxWidth;
    int newHeight = maxHeight;

    if (aspectRatio > 1) {
      // Landscape orientation
      newHeight = (maxWidth / aspectRatio).round();
      if (newHeight > maxHeight) {
        newHeight = maxHeight;
        newWidth = (maxHeight * aspectRatio).round();
      }
    } else {
      // Portrait orientation
      newWidth = (maxHeight * aspectRatio).round();
      if (newWidth > maxWidth) {
        newWidth = maxWidth;
        newHeight = (maxWidth / aspectRatio).round();
      }
    }

    // Resize image with high quality
    return img.copyResize(
      image,
      width: newWidth,
      height: newHeight,
      interpolation: img.Interpolation.cubic,
    );
  }

  /// Get optimal compression settings based on image size
  Map<String, int> getOptimalCompressionSettings(img.Image image) {
    final int pixels = image.width * image.height;

    if (pixels > 8000000) { // > 8MP
      return {'maxWidth': 1920, 'maxHeight': 1080, 'quality': 80};
    } else if (pixels > 4000000) { // > 4MP
      return {'maxWidth': 1920, 'maxHeight': 1080, 'quality': 85};
    } else if (pixels > 2000000) { // > 2MP
      return {'maxWidth': 1600, 'maxHeight': 1200, 'quality': 90};
    } else {
      return {'maxWidth': 1280, 'maxHeight': 960, 'quality': 95};
    }
  }

  /// Estimate file size reduction
  String estimateCompressionRatio(File originalFile, img.Image image, int quality) {
    final int originalSize = originalFile.lengthSync();
    final int pixels = image.width * image.height;

    // Rough estimation based on quality and pixel count
    double estimatedRatio = (quality / 100) * (pixels / 2000000);
    estimatedRatio = estimatedRatio.clamp(0.1, 1.0);

    final double estimatedSize = originalSize * estimatedRatio;
    final double reductionPercent = ((originalSize - estimatedSize) / originalSize) * 100;

    return '${reductionPercent.toStringAsFixed(0)}% smaller';
  }

  /// Format file size in human readable format
  String formatFileSize(int bytes) {
    if (bytes < 1024) {
      return '$bytes B';
    } else if (bytes < 1024 * 1024) {
      return '${(bytes / 1024).toStringAsFixed(1)} KB';
    } else if (bytes < 1024 * 1024 * 1024) {
      return '${(bytes / (1024 * 1024)).toStringAsFixed(1)} MB';
    } else {
      return '${(bytes / (1024 * 1024 * 1024)).toStringAsFixed(1)} GB';
    }
  }

  /// Get compression info for display
  Map<String, String> getCompressionInfo(File originalFile, File compressedFile, img.Image originalImage, img.Image compressedImage) {
    final int originalSize = originalFile.lengthSync();
    final int compressedSize = compressedFile.lengthSync();
    final double reductionPercent = ((originalSize - compressedSize) / originalSize) * 100;

    return {
      'originalSize': formatFileSize(originalSize),
      'compressedSize': formatFileSize(compressedSize),
      'reduction': '${reductionPercent.toStringAsFixed(1)}%',
      'originalDimensions': '${originalImage.width}x${originalImage.height}',
      'compressedDimensions': '${compressedImage.width}x${compressedImage.height}',
    };
  }

  /// Add watermark with custom styling and smart compression
  Future<File?> addStyledWatermarkToImage({
    required File imageFile,
    required String title,
    required String description,
    required String locationName,
    required double latitude,
    required double longitude,
    required DateTime capturedAt,
    int backgroundOpacity = 180,
    img.Color? textColor,
    img.Color? backgroundColor,
    bool autoCompress = true,
  }) async {
    try {
      final Uint8List imageBytes = await imageFile.readAsBytes();
      img.Image? image = img.decodeImage(imageBytes);

      if (image == null) {
        throw Exception('Failed to decode image');
      }

      // Get optimal compression settings
      Map<String, int> compressionSettings = autoCompress
          ? getOptimalCompressionSettings(image)
          : {'maxWidth': image.width, 'maxHeight': image.height, 'quality': 90};

      // Compress image if needed
      if (autoCompress) {
        image = _compressImage(
          image,
          compressionSettings['maxWidth']!,
          compressionSettings['maxHeight']!
        );
      }

      // Create watermark with custom styling
      image = _addStyledTextWatermark(
        image,
        title: title,
        description: description,
        locationName: locationName,
        latitude: latitude,
        longitude: longitude,
        capturedAt: capturedAt,
        textColor: textColor ?? img.ColorRgba8(255, 255, 255, 255),
        backgroundColor: backgroundColor ?? img.ColorRgba8(0, 0, 0, 180),
      );

      // Save the modified image with optimal quality
      final String outputPath = imageFile.path.replaceAll('.jpg', '_watermarked.jpg');
      final File outputFile = File(outputPath);
      await outputFile.writeAsBytes(
        img.encodeJpg(image, quality: compressionSettings['quality']!)
      );

      return outputFile;
    } catch (e) {
      print('Error adding styled watermark: $e');
      return null;
    }
  }

  /// Add styled text watermark to image with proper padding and text wrapping
  img.Image _addStyledTextWatermark(
    img.Image image, {
    required String title,
    required String description,
    required String locationName,
    required double latitude,
    required double longitude,
    required DateTime capturedAt,
    required img.Color textColor,
    required img.Color backgroundColor,
  }) {
    final int imageWidth = image.width;
    final int imageHeight = image.height;

    // Fixed padding 40px for all sides
    final int horizontalPadding = 40;
    final int verticalPadding = 40;
    final int availableTextWidth = imageWidth - (horizontalPadding * 2);

    // Format information
    final String formattedDate = DateFormat('EEEE, dd MMMM yyyy', 'id_ID').format(capturedAt);
    final String formattedTime = DateFormat('HH:mm:ss').format(capturedAt);
    final String coordinates = 'Lat: ${latitude.toStringAsFixed(6)}, Lng: ${longitude.toStringAsFixed(6)}';

    // Create watermark content with potential wrapping
    final List<String> originalLines = [
      '📋 $title',
      '📅 $formattedDate',
      '🕐 $formattedTime',
      '📍 $locationName',
      '🌐 $coordinates',
    ];

    if (description.isNotEmpty) {
      originalLines.add('📝 $description');
    }

    // Wrap long lines
    final List<String> wrappedLines = [];
    for (String line in originalLines) {
      wrappedLines.addAll(_wrapText(line, availableTextWidth, imageWidth));
    }

    // Calculate dimensions
    final int fontSize = _calculateFontSize(imageWidth);
    final int lineHeight = (fontSize * 1.3).round();

    // Calculate required watermark height
    final int requiredHeight = (wrappedLines.length * lineHeight) + (verticalPadding * 2);
    final int watermarkHeight = requiredHeight.clamp(
      (imageHeight * 0.15).round(), // Minimum 15% of image height
      (imageHeight * 0.4).round(),  // Maximum 40% of image height
    );
    final int watermarkY = imageHeight - watermarkHeight;

    // Add background with gradient effect (optional enhancement)
    img.fillRect(
      image,
      x1: 0,
      y1: watermarkY,
      x2: imageWidth,
      y2: imageHeight,
      color: backgroundColor,
    );

    // Add subtle border at the top
    img.drawLine(
      image,
      x1: 0,
      y1: watermarkY,
      x2: imageWidth,
      y2: watermarkY,
      color: img.ColorRgba8(255, 255, 255, 100),
      thickness: 2,
    );

    // Draw text lines with proper spacing
    int currentY = watermarkY + verticalPadding;

    for (int i = 0; i < wrappedLines.length; i++) {
      final String line = wrappedLines[i];

      // Make sure text fits within watermark area
      if (currentY + lineHeight <= imageHeight - verticalPadding) {
        img.drawString(
          image,
          line,
          font: img.arial24,
          x: horizontalPadding,
          y: currentY,
          color: textColor,
        );

        currentY += lineHeight;
      } else {
        // If we run out of space, stop drawing
        break;
      }
    }

    return image;
  }
}
