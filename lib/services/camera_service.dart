import 'dart:io';
import 'package:camera/camera.dart';
import 'package:image_picker/image_picker.dart';
import 'package:gal/gal.dart';
import 'package:path_provider/path_provider.dart';
import 'package:path/path.dart' as path;
import 'watermark_service.dart';

class CameraService {
  static final CameraService _instance = CameraService._internal();
  factory CameraService() => _instance;
  CameraService._internal();

  final ImagePicker _picker = ImagePicker();
  List<CameraDescription>? _cameras;

  // Initialize cameras
  Future<void> initializeCameras() async {
    try {
      _cameras = await availableCameras();
    } catch (e) {
      print('Error initializing cameras: $e');
    }
  }

  // Get available cameras
  List<CameraDescription>? get cameras => _cameras;

  // Take photo with camera
  Future<File?> takePhoto() async {
    try {
      final XFile? photo = await _picker.pickImage(
        source: ImageSource.camera,
        imageQuality: 85,
      );

      if (photo != null) {
        return File(photo.path);
      }
      return null;
    } catch (e) {
      print('Error taking photo: $e');
      return null;
    }
  }

  // Pick image from gallery
  Future<File?> pickImageFromGallery() async {
    try {
      final XFile? image = await _picker.pickImage(
        source: ImageSource.gallery,
        imageQuality: 85,
      );

      if (image != null) {
        return File(image.path);
      }
      return null;
    } catch (e) {
      print('Error picking image: $e');
      return null;
    }
  }

  // Save image to app directory
  Future<String?> saveImageToAppDirectory(File imageFile) async {
    try {
      final Directory appDir = await getApplicationDocumentsDirectory();
      final String fileName = '${DateTime.now().millisecondsSinceEpoch}.jpg';
      final String filePath = path.join(appDir.path, 'photos', fileName);

      // Create photos directory if it doesn't exist
      final Directory photosDir = Directory(path.dirname(filePath));
      if (!await photosDir.exists()) {
        await photosDir.create(recursive: true);
      }

      // Copy image to app directory
      final File savedImage = await imageFile.copy(filePath);
      return savedImage.path;
    } catch (e) {
      print('Error saving image to app directory: $e');
      return null;
    }
  }

  // Save image to device gallery
  Future<bool> saveImageToGallery(File imageFile) async {
    try {
      await Gal.putImage(imageFile.path);
      return true;
    } catch (e) {
      print('Error saving image to gallery: $e');
      return false;
    }
  }

  // Save image with metadata overlay (watermark)
  Future<String?> saveImageWithMetadata({
    required File imageFile,
    required String title,
    required String description,
    required String locationName,
    required double latitude,
    required double longitude,
    required DateTime capturedAt,
  }) async {
    try {
      final WatermarkService watermarkService = WatermarkService();

      // Add watermark to image
      final File? watermarkedImage = await watermarkService.addStyledWatermarkToImage(
        imageFile: imageFile,
        title: title,
        description: description,
        locationName: locationName,
        latitude: latitude,
        longitude: longitude,
        capturedAt: capturedAt,
      );

      if (watermarkedImage == null) {
        throw Exception('Failed to add watermark to image');
      }

      // Save watermarked image to app directory
      final String? savedPath = await saveImageToAppDirectory(watermarkedImage);

      if (savedPath != null) {
        // Also save to gallery
        await saveImageToGallery(File(savedPath));

        // Clean up temporary watermarked file if it's different from saved path
        if (watermarkedImage.path != savedPath) {
          try {
            await watermarkedImage.delete();
          } catch (e) {
            print('Warning: Could not delete temporary file: $e');
          }
        }
      }

      return savedPath;
    } catch (e) {
      print('Error saving image with metadata: $e');
      return null;
    }
  }

  // Delete image file
  Future<bool> deleteImageFile(String filePath) async {
    try {
      final File file = File(filePath);
      if (await file.exists()) {
        await file.delete();
        return true;
      }
      return false;
    } catch (e) {
      print('Error deleting image file: $e');
      return false;
    }
  }
}
