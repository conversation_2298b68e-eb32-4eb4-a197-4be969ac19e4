import 'package:sqflite/sqflite.dart';
import 'package:path/path.dart';
import '../models/photo_documentation.dart';

class DatabaseService {
  static final DatabaseService _instance = DatabaseService._internal();
  factory DatabaseService() => _instance;
  DatabaseService._internal();

  static Database? _database;

  Future<Database> get database async {
    if (_database != null) return _database!;
    _database = await _initDatabase();
    return _database!;
  }

  Future<Database> _initDatabase() async {
    String path = join(await getDatabasesPath(), 'work_documentation.db');
    return await openDatabase(
      path,
      version: 1,
      onCreate: _onCreate,
    );
  }

  Future<void> _onCreate(Database db, int version) async {
    await db.execute('''
      CREATE TABLE photo_documentations(
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        imagePath TEXT NOT NULL,
        title TEXT NOT NULL,
        description TEXT NOT NULL,
        latitude REAL NOT NULL,
        longitude REAL NOT NULL,
        locationName TEXT NOT NULL,
        capturedAt INTEGER NOT NULL,
        userId TEXT NOT NULL
      )
    ''');
  }

  // Insert photo documentation
  Future<int> insertPhotoDocumentation(PhotoDocumentation photo) async {
    final db = await database;
    return await db.insert('photo_documentations', photo.toMap());
  }

  // Get all photo documentations for a user
  Future<List<PhotoDocumentation>> getPhotoDocumentations(String userId) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'photo_documentations',
      where: 'userId = ?',
      whereArgs: [userId],
      orderBy: 'capturedAt DESC',
    );

    return List.generate(maps.length, (i) {
      return PhotoDocumentation.fromMap(maps[i]);
    });
  }

  // Get photo documentations with filters
  Future<List<PhotoDocumentation>> getFilteredPhotoDocumentations({
    required String userId,
    String? searchQuery,
    DateTime? startDate,
    DateTime? endDate,
    String orderBy = 'capturedAt DESC',
  }) async {
    final db = await database;
    
    String whereClause = 'userId = ?';
    List<dynamic> whereArgs = [userId];

    if (searchQuery != null && searchQuery.isNotEmpty) {
      whereClause += ' AND (title LIKE ? OR description LIKE ? OR locationName LIKE ?)';
      whereArgs.addAll(['%$searchQuery%', '%$searchQuery%', '%$searchQuery%']);
    }

    if (startDate != null) {
      whereClause += ' AND capturedAt >= ?';
      whereArgs.add(startDate.millisecondsSinceEpoch);
    }

    if (endDate != null) {
      whereClause += ' AND capturedAt <= ?';
      whereArgs.add(endDate.millisecondsSinceEpoch);
    }

    final List<Map<String, dynamic>> maps = await db.query(
      'photo_documentations',
      where: whereClause,
      whereArgs: whereArgs,
      orderBy: orderBy,
    );

    return List.generate(maps.length, (i) {
      return PhotoDocumentation.fromMap(maps[i]);
    });
  }

  // Update photo documentation
  Future<int> updatePhotoDocumentation(PhotoDocumentation photo) async {
    final db = await database;
    return await db.update(
      'photo_documentations',
      photo.toMap(),
      where: 'id = ?',
      whereArgs: [photo.id],
    );
  }

  // Delete photo documentation
  Future<int> deletePhotoDocumentation(int id) async {
    final db = await database;
    return await db.delete(
      'photo_documentations',
      where: 'id = ?',
      whereArgs: [id],
    );
  }

  // Get photo documentation by ID
  Future<PhotoDocumentation?> getPhotoDocumentationById(int id) async {
    final db = await database;
    final List<Map<String, dynamic>> maps = await db.query(
      'photo_documentations',
      where: 'id = ?',
      whereArgs: [id],
    );

    if (maps.isNotEmpty) {
      return PhotoDocumentation.fromMap(maps.first);
    }
    return null;
  }
}
