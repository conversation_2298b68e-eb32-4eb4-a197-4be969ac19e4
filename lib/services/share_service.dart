import 'dart:io';
import 'package:share_plus/share_plus.dart';
import 'package:url_launcher/url_launcher.dart';
import '../models/photo_documentation.dart';
import 'package:intl/intl.dart';

class ShareService {
  static final ShareService _instance = ShareService._internal();
  factory ShareService() => _instance;
  ShareService._internal();

  /// Share photo to WhatsApp with documentation details
  Future<bool> shareToWhatsApp({
    required File imageFile,
    required PhotoDocumentation photoDoc,
    String? phoneNumber,
  }) async {
    try {
      // Create message text with documentation details
      final String message = _createShareMessage(photoDoc);
      
      if (phoneNumber != null && phoneNumber.isNotEmpty) {
        // Share to specific WhatsApp contact
        return await _shareToWhatsAppContact(
          imageFile: imageFile,
          message: message,
          phoneNumber: phoneNumber,
        );
      } else {
        // Share to WhatsApp (user chooses contact)
        return await _shareToWhatsAppGeneral(
          imageFile: imageFile,
          message: message,
        );
      }
    } catch (e) {
      print('Error sharing to WhatsApp: $e');
      return false;
    }
  }

  /// Share photo with general share dialog
  Future<bool> sharePhoto({
    required File imageFile,
    required PhotoDocumentation photoDoc,
  }) async {
    try {
      final String message = _createShareMessage(photoDoc);
      
      await Share.shareXFiles(
        [XFile(imageFile.path)],
        text: message,
        subject: 'Dokumentasi: ${photoDoc.title}',
      );
      
      return true;
    } catch (e) {
      print('Error sharing photo: $e');
      return false;
    }
  }

  /// Share to WhatsApp with specific contact
  Future<bool> _shareToWhatsAppContact({
    required File imageFile,
    required String message,
    required String phoneNumber,
  }) async {
    try {
      // Clean phone number (remove non-digits)
      final String cleanNumber = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');
      
      // Create WhatsApp URL
      final String whatsappUrl = 'https://wa.me/$cleanNumber?text=${Uri.encodeComponent(message)}';
      
      // Launch WhatsApp
      if (await canLaunchUrl(Uri.parse(whatsappUrl))) {
        await launchUrl(Uri.parse(whatsappUrl), mode: LaunchMode.externalApplication);
        
        // Note: We can't directly share image with URL method
        // User will need to manually attach the image
        // For direct image sharing, we use the general share method
        return true;
      } else {
        // Fallback to general share
        return await _shareToWhatsAppGeneral(
          imageFile: imageFile,
          message: message,
        );
      }
    } catch (e) {
      print('Error sharing to WhatsApp contact: $e');
      return false;
    }
  }

  /// Share to WhatsApp using general share
  Future<bool> _shareToWhatsAppGeneral({
    required File imageFile,
    required String message,
  }) async {
    try {
      // Share image with text using share_plus
      await Share.shareXFiles(
        [XFile(imageFile.path)],
        text: message,
        subject: 'Dokumentasi Kerja',
      );
      
      return true;
    } catch (e) {
      print('Error sharing to WhatsApp general: $e');
      return false;
    }
  }

  /// Create formatted message for sharing
  String _createShareMessage(PhotoDocumentation photoDoc) {
    final String formattedDate = DateFormat('EEEE, dd MMMM yyyy', 'id_ID').format(photoDoc.capturedAt);
    final String formattedTime = DateFormat('HH:mm:ss').format(photoDoc.capturedAt);
    
    String message = '📋 *${photoDoc.title}*\n\n';
    message += '📅 $formattedDate\n';
    message += '🕐 $formattedTime\n';
    message += '📍 ${photoDoc.locationName}\n';
    message += '🌐 Lat: ${photoDoc.latitude.toStringAsFixed(6)}, Lng: ${photoDoc.longitude.toStringAsFixed(6)}\n';
    
    if (photoDoc.description.isNotEmpty) {
      message += '\n📝 ${photoDoc.description}\n';
    }
    
    message += '\n_Dokumentasi Kerja - Generated by Work Documentation App_';
    
    return message;
  }

  /// Share text only (without image)
  Future<bool> shareTextOnly({
    required PhotoDocumentation photoDoc,
    String? phoneNumber,
  }) async {
    try {
      final String message = _createShareMessage(photoDoc);
      
      if (phoneNumber != null && phoneNumber.isNotEmpty) {
        // Share to specific WhatsApp contact
        final String cleanNumber = phoneNumber.replaceAll(RegExp(r'[^\d]'), '');
        final String whatsappUrl = 'https://wa.me/$cleanNumber?text=${Uri.encodeComponent(message)}';
        
        if (await canLaunchUrl(Uri.parse(whatsappUrl))) {
          await launchUrl(Uri.parse(whatsappUrl), mode: LaunchMode.externalApplication);
          return true;
        }
      }
      
      // Fallback to general share
      await Share.share(message, subject: 'Dokumentasi: ${photoDoc.title}');
      return true;
    } catch (e) {
      print('Error sharing text: $e');
      return false;
    }
  }

  /// Check if WhatsApp is installed
  Future<bool> isWhatsAppInstalled() async {
    try {
      const String whatsappUrl = 'whatsapp://';
      return await canLaunchUrl(Uri.parse(whatsappUrl));
    } catch (e) {
      return false;
    }
  }

  /// Get share options based on available apps
  Future<List<ShareOption>> getAvailableShareOptions() async {
    List<ShareOption> options = [];
    
    // Always available
    options.add(ShareOption(
      name: 'Share',
      icon: 'share',
      description: 'Bagikan ke aplikasi lain',
    ));
    
    // Check WhatsApp
    if (await isWhatsAppInstalled()) {
      options.add(ShareOption(
        name: 'WhatsApp',
        icon: 'whatsapp',
        description: 'Bagikan ke WhatsApp',
      ));
    }
    
    return options;
  }

  /// Create shareable link (if needed for web sharing)
  String createShareableText(PhotoDocumentation photoDoc) {
    return _createShareMessage(photoDoc);
  }
}

class ShareOption {
  final String name;
  final String icon;
  final String description;

  ShareOption({
    required this.name,
    required this.icon,
    required this.description,
  });
}
